// Remove this line:
// package com.mycompany.lab05rentanapartment;

public class Tenant {
    private String name;
    private int floor;

    public Tenant() {
        // Default constructor
    }

    public Tenant(String name, int floor) {
        setName(name);
        this.floor = floor;
    }

    public void setName(String name) {
      
        int spaceIndex = name.indexOf(" ");
        if (spaceIndex != -1) {
            String firstName = name.substring(0, spaceIndex);
            String lastName = name.substring(spaceIndex + 1);
            this.name = StringUtility.capitalizeFirst(firstName) + " " + StringUtility.capitalizeFirst(lastName);
        } else {
          
            this.name = StringUtility.capitalizeFirst(name);
        }
    }

    public String getName() {
        return name;
    }

    public void setFloor(int floor) {
        this.floor = floor;
    }

    public int getFloor() {
        return floor;
    }

    @Override
    public String toString() {
        return  name + " wants to rent Floor " + floor;
    }
}
