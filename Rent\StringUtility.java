public class StringUtility {

    /*
    A utility method that helps to create a new string that is the reversal of str
    */
    static String reverse(String str){
        String reversal = "";
        int length = str.length();
        for(int i = length - 1; i >= 0; i--){ //begin from the end and stop at the start of str to reverse it
            reversal += str.charAt(i); //append to reversal as we read the characters at index i
        }
        return reversal;
    }

    static boolean isPalindrome(String str){
        String reversal = reverse(str);
        //compares if the reversal is the same as the original string
        return str.equals(reversal);
        //OR, just one statement
        //return str.equals(reverse(str));
    }

    /*
    A utility method that helps me get the file extension
    */
    /*
    static String fileExtension(String fileName){
        int dot = fileName.lastIndexOf('.'); //find the position of last period
        return fileName.substring(dot + 1); //return a substring beginning from index after period to the end.
    }
    */

    /*
    A utility method that helps me get the file name without extension
    */
    /*
    static String fileName(String file){
        int dot = file.indexOf('.'); //I need to find the position of period
        return file.substring(0, dot); //return a substring from beginning to the index before period
    }
    */

    /*
    A utility method to capitalize the first character of a given string.
    */
static String capitalizeFirst(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        char firstChar = Character.toUpperCase(input.charAt(0));
        String rest = input.substring(1);
        return firstChar + rest;
    }
}
