import java.util.Scanner;

public class Rent {
private int[] rentRates;  
private Tenant tenant;

    public Rent() {
        rentRates = new int[5]; 
        tenant = new Tenant();  
        setRentRates();         // Set the rent prices
    }

    private void setRentRates() {
        rentRates[0] = 500;
        rentRates[1] = 650;
        rentRates[2] = 800;
        rentRates[3] = 950;
        rentRates[4] = 1100;
    }

    public void readTenantInfo(String name, int floor) {
        tenant.setName(name);  
        tenant.setFloor(floor);
    }

    private int calculateRent() {
        return rentRates[tenant.getFloor() - 1];
    }

    @Override
    public String toString() {
        double rentAmount = (double) calculateRent(); 
        return tenant.toString() + "\nRent= $" + String.format("%.1f", rentAmount);
    }
}
